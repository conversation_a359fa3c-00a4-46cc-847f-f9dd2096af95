-> Building extproc for linux/amd64
go: downloading github.com/envoyproxy/go-control-plane/envoy v1.32.5-0.20250622153809-434b6986176d
go: downloading github.com/prometheus/client_golang v1.22.0
go: downloading go.opentelemetry.io/otel/exporters/prometheus v0.59.0
go: downloading go.opentelemetry.io/otel v1.37.0
go: downloading go.opentelemetry.io/otel/metric v1.37.0
go: downloading go.opentelemetry.io/otel/sdk/metric v1.37.0
go: downloading go.opentelemetry.io/otel/sdk v1.37.0
go: downloading google.golang.org/grpc v1.73.0
go: downloading github.com/envoyproxy/go-control-plane v0.13.5-0.20250622153809-434b6986176d
go: downloading github.com/google/cel-go v0.25.0
go: downloading google.golang.org/protobuf v1.36.6
go: downloading github.com/beorn7/perks v1.0.1
go: downloading github.com/cespare/xxhash/v2 v2.3.0
go: downloading github.com/prometheus/client_model v0.6.2
go: downloading github.com/prometheus/common v0.65.0
go: downloading github.com/prometheus/procfs v0.16.1
go: downloading golang.org/x/net v0.41.0
go: downloading k8s.io/apimachinery v0.34.0-alpha.0
go: downloading github.com/aws/aws-sdk-go-v2 v1.36.5
go: downloading github.com/aws/aws-sdk-go-v2/config v1.29.17
go: downloading github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.6.11
go: downloading github.com/google/uuid v1.6.0
go: downloading github.com/tidwall/sjson v1.2.5
go: downloading google.golang.org/genai v1.14.0
go: downloading k8s.io/utils v0.0.0-20250502105355-0f33e8f1c979
go: downloading github.com/cncf/xds/go v0.0.0-20250326154945-ae57f3c0d45f
go: downloading github.com/envoyproxy/protoc-gen-validate v1.2.1
go: downloading google.golang.org/genproto/googleapis/rpc v0.0.0-20250603155806-513f23925822
go: downloading cel.dev/expr v0.24.0
go: downloading google.golang.org/genproto/googleapis/api v0.0.0-20250603155806-513f23925822
go: downloading github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822
go: downloading github.com/go-logr/logr v1.4.3
go: downloading go.opentelemetry.io/otel/trace v1.37.0
go: downloading github.com/go-logr/stdr v1.2.2
go: downloading go.opentelemetry.io/auto/sdk v1.1.0
go: downloading github.com/aws/smithy-go v1.22.4
go: downloading github.com/aws/aws-sdk-go-v2/credentials v1.17.70
go: downloading github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.32
go: downloading github.com/aws/aws-sdk-go-v2/internal/ini v1.8.3
go: downloading github.com/aws/aws-sdk-go-v2/service/sso v1.25.5
go: downloading github.com/aws/aws-sdk-go-v2/service/ssooidc v1.30.3
go: downloading github.com/aws/aws-sdk-go-v2/service/sts v1.34.0
go: downloading github.com/tidwall/gjson v1.18.0
go: downloading cloud.google.com/go/auth v0.15.0
go: downloading cloud.google.com/go v0.116.0
go: downloading github.com/google/go-cmp v0.7.0
go: downloading github.com/gorilla/websocket v1.5.4-0.20250319132907-e064f32e3674
go: downloading github.com/stoewer/go-strcase v1.3.0
go: downloading github.com/antlr4-go/antlr/v4 v4.13.1
go: downloading sigs.k8s.io/yaml v1.5.0
go: downloading github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.36
go: downloading github.com/tidwall/match v1.1.1
go: downloading github.com/tidwall/pretty v1.2.1
go: downloading github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.12.4
go: downloading github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.12.17
go: downloading github.com/googleapis/gax-go/v2 v2.14.1
go: downloading cloud.google.com/go/compute/metadata v0.7.0
go: downloading go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.61.0
go: downloading sigs.k8s.io/json v0.0.0-20241014173422-cfa47c3a1cc8
go: downloading golang.org/x/text v0.26.0
go: downloading github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.36
go: downloading golang.org/x/exp v0.0.0-20250606033433-dcc06ee1d476
go: downloading go.yaml.in/yaml/v2 v2.4.2
go: downloading github.com/google/s2a-go v0.1.9
go: downloading github.com/googleapis/enterprise-certificate-proxy v0.3.4
go: downloading github.com/felixge/httpsnoop v1.0.4
go: downloading golang.org/x/crypto v0.39.0
<- Built out/extproc-linux-amd64
make[1]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
-> Building extproc_custom_metrics for linux/amd64
<- Built out/extproc_custom_metrics-linux-amd64
make[1]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[1]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
-> Building testupstream for linux/amd64
<- Built out/testupstream-linux-amd64
make[1]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
Run ExtProc test
go: downloading github.com/openai/openai-go v1.8.2
go: downloading github.com/stretchr/testify v1.10.0
go: downloading sigs.k8s.io/controller-runtime v0.21.0
go: downloading github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
go: downloading github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2
go: downloading gopkg.in/yaml.v3 v3.0.1
go: downloading github.com/evanphx/json-patch/v5 v5.9.11
go: downloading k8s.io/client-go v0.33.2
go: downloading github.com/evanphx/json-patch v5.9.11+incompatible
go: downloading k8s.io/klog/v2 v2.130.1
go: downloading github.com/gogo/protobuf v1.3.2
go: downloading sigs.k8s.io/randfill v1.0.0
go: downloading sigs.k8s.io/structured-merge-diff/v4 v4.7.0
go: downloading k8s.io/kube-openapi v0.0.0-20250318190949-c8a335a9a2ff
go: downloading gopkg.in/inf.v0 v0.9.1
go: downloading k8s.io/api v0.33.2
go: downloading github.com/google/gnostic-models v0.6.9
go: downloading github.com/fxamacker/cbor/v2 v2.7.0
go: downloading github.com/json-iterator/go v1.1.12
go: downloading github.com/go-openapi/jsonreference v0.21.0
go: downloading github.com/go-openapi/swag v0.23.1
go: downloading golang.org/x/term v0.32.0
go: downloading golang.org/x/oauth2 v0.30.0
go: downloading golang.org/x/time v0.11.0
go: downloading github.com/x448/float16 v0.8.4
go: downloading github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
go: downloading github.com/modern-go/reflect2 v1.0.2
go: downloading github.com/go-openapi/jsonpointer v0.21.1
go: downloading github.com/mailru/easyjson v0.9.0
go: downloading github.com/emicklei/go-restful/v3 v3.12.1
go: downloading github.com/josharian/intern v1.0.0
[testupstream] Version:  cc195230e1131308d9ea0b3b352a90cc5e6c6e88
[testupstream] header "X-Response-Body": [eyJjaG9pY2VzIjpbeyJtZXNzYWdlIjp7ImNvbnRlbnQiOiJUaGlzIGlzIGEgdGVzdC4ifX1dfQ==]
[testupstream] header "X-Stainless-Lang": [go]
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Request-Id": [1b3d4ef1-eac9-4c4c-8121-1e6b0f278355]
[testupstream] header "Accept": [application/json]
[testupstream] header "X-Stainless-Package-Version": [1.8.2]
[testupstream] header "X-Model-Name": [something-cool]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "User-Agent": [OpenAI/Go 1.8.2]
[testupstream] header "X-Expected-Path": [L3YxL2NoYXQvY29tcGxldGlvbnM=]
[testupstream] header "X-Stainless-Os": [Linux]
[testupstream] header "X-Stainless-Retry-Count": [0]
[testupstream] header "X-Stainless-Runtime": [go]
[testupstream] header "X-Stainless-Runtime-Version": [go1.24.4]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "Content-Length": [86]
[testupstream] header "Content-Type": [application/json]
[testupstream] header "X-Stainless-Arch": [x64]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"choices":[{"message":{"content":"This is a test."}}]}
[testupstream] Version:  cc195230e1131308d9ea0b3b352a90cc5e6c6e88
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "Content-Length": [77]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Path": [L21vZGVsL3NvbWV0aGluZy9jb252ZXJzZQ==]
[testupstream] header "X-Expected-Request-Body": [eyJpbmZlcmVuY2VDb25maWciOnt9LCJtZXNzYWdlcyI6W10sInN5c3RlbSI6W3sidGV4dCI6IllvdSBhcmUgYSBjaGF0Ym90LiJ9XX0=]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Request-Id": [b163fabd-8c81-4725-8c4d-7fe0fa9b62ef]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "X-Response-Body": [eyJvdXRwdXQiOnsibWVzc2FnZSI6eyJjb250ZW50IjpbeyJ0ZXh0IjoicmVzcG9uc2UifSx7InRleHQiOiJmcm9tIn0seyJ0ZXh0IjoiYXNzaXN0YW50In1dLCJyb2xlIjoiYXNzaXN0YW50In19LCJzdG9wUmVhc29uIjpudWxsLCJ1c2FnZSI6eyJpbnB1dFRva2VucyI6MTAsIm91dHB1dFRva2VucyI6MjAsInRvdGFsVG9rZW5zIjozMH19]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Test-Backend": [aws-bedrock]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no response headers
[testupstream] response sent: {"output":{"message":{"content":[{"text":"response"},{"text":"from"},{"text":"assistant"}],"role":"assistant"}},"stopReason":null,"usage":{"inputTokens":10,"outputTokens":20,"totalTokens":30}}
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "X-Request-Id": [ffbdb4a5-dbb8-40bc-bed6-11da5be5415e]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "X-Response-Body": [eyJjaG9pY2VzIjpbeyJtZXNzYWdlIjp7ImNvbnRlbnQiOiJUaGlzIGlzIGEgdGVzdC4ifX1dfQ==]
[testupstream] header "X-Response-Status": []
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "Content-Length": [83]
[testupstream] header "X-Expected-Path": [L3YxL2NoYXQvY29tcGxldGlvbnM=]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"choices":[{"message":{"content":"This is a test."}}]}
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "Content-Length": [83]
[testupstream] header "X-Response-Type": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Request-Id": [7359f70f-29e7-4fb6-9d50-4d527fa90ef6]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "X-Expected-Path": [L3YxL2NoYXQvY29tcGxldGlvbnM=]
[testupstream] header "X-Response-Body": [eyJjaG9pY2VzIjpbeyJtZXNzYWdlIjp7ImNvbnRlbnQiOiJUaGlzIGlzIGEgdGVzdC4ifX1dfQ==]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Model-Name": [something]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: ���VJ���LN-V���V�M-.NLOU��VJ��+I�+Q�R
��,V�,VHT(I-.�S�������-7
[testupstream] header "X-Expected-Path": [L29wZW5haS9kZXBsb3ltZW50cy9zb21ldGhpbmcvY2hhdC9jb21wbGV0aW9ucw==]
[testupstream] header "X-Test-Backend": [azure-openai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Request-Id": [a80a6de1-862c-43f2-a30c-3860c23585af]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Response-Body": [eyJjaG9pY2VzIjpbeyJtZXNzYWdlIjp7ImNvbnRlbnQiOiJUaGlzIGlzIGEgdGVzdC4ifX1dfQ==]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "Content-Length": [83]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"choices":[{"message":{"content":"This is a test."}}]}
[testupstream] header "X-Expected-Host": [gcp-region-aiplatform.googleapis.com]
[testupstream] header "X-Response-Body": [eyJjYW5kaWRhdGVzIjpbeyJjb250ZW50Ijp7InBhcnRzIjpbeyJ0ZXh0IjoiVGhpcyBpcyBhIHRlc3QgcmVzcG9uc2UgZnJvbSBHZW1pbmkuIn1dLCJyb2xlIjoibW9kZWwifSwiZmluaXNoUmVhc29uIjoiU1RPUCJ9XSwidXNhZ2VNZXRhZGF0YSI6eyJwcm9tcHRUb2tlbkNvdW50IjoxNSwiY2FuZGlkYXRlc1Rva2VuQ291bnQiOjEwLCJ0b3RhbFRva2VuQ291bnQiOjI1fX0=]
[testupstream] header "X-Request-Id": [ae247d7d-144a-446a-abba-4eb3db49d6db]
[testupstream] header "X-Expected-Headers": [QXV0aG9yaXphdGlvbjpCZWFyZXIgZmFrZS1nY3AtYXV0aC10b2tlbg==]
[testupstream] header "X-Expected-Path": [L3YxL3Byb2plY3RzL2djcC1wcm9qZWN0LW5hbWUvbG9jYXRpb25zL2djcC1yZWdpb24vcHVibGlzaGVycy9nb29nbGUvbW9kZWxzL2dlbWluaS0xLjUtcHJvOmdlbmVyYXRlQ29udGVudA==]
[testupstream] header "X-Response-Status": [200]
[testupstream] header "X-Test-Backend": [gcp-vertexai]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Model-Name": [gemini-1.5-pro]
[testupstream] header "Authorization": [***
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Request-Body": [eyJjb250ZW50cyI6bnVsbCwidG9vbHMiOm51bGwsImdlbmVyYXRpb25fY29uZmlnIjp7fSwic3lzdGVtX2luc3RydWN0aW9uIjp7InBhcnRzIjpbeyJ0ZXh0IjoiWW91IGFyZSBhIGhlbHBmdWwgYXNzaXN0YW50LiJ9XX19]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "Content-Length": [126]
[testupstream] host matched: gcp-region-aiplatform.googleapis.com
[testupstream] expected headers Authorization:***
[testupstream] header "Authorization" matched ***
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no response headers
[testupstream] response sent: {"candidates":[{"content":{"parts":[{"text":"This is a test response from Gemini."}],"role":"model"},"finishReason":"STOP"}],"usageMetadata":{"promptTokenCount":15,"candidatesTokenCount":10,"totalTokenCount":25}}
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "Content-Length": [88]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Path": [L3YxL2NoYXQvY29tcGxldGlvbnM=]
[testupstream] header "X-Expected-Request-Body": [eyJtb2RlbCI6Im92ZXJyaWRlLW1vZGVsIiwibWVzc2FnZXMiOlt7InJvbGUiOiJzeXN0ZW0iLCJjb250ZW50IjoiWW91IGFyZSBhIGNoYXRib3QuIn1dfQ==]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Request-Id": [ca3b9444-4910-48b4-81d9-cac3db315ffe]
[testupstream] header "X-Model-Name": [requested-model]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "X-Response-Body": [eyJjaG9pY2VzIjpbeyJtZXNzYWdlIjp7ImNvbnRlbnQiOiJUaGlzIGlzIGEgdGVzdC4ifX1dfQ==]
[testupstream] header "X-Test-Backend": [modelname-override]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no response headers
[testupstream] response sent: {"choices":[{"message":{"content":"This is a test."}}]}
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Path": [L21vZGVsL3NvbWV0aGluZy9jb252ZXJzZS1zdHJlYW0=]
[testupstream] header "X-Expected-Request-Body": [eyJpbmZlcmVuY2VDb25maWciOnt9LCJtZXNzYWdlcyI6W10sInN5c3RlbSI6W3sidGV4dCI6IllvdSBhcmUgYSBjaGF0Ym90LiJ9XX0=]
[testupstream] header "X-Response-Body": [eyJyb2xlIjoiYXNzaXN0YW50In0KeyJzdGFydCI6eyJ0b29sVXNlIjp7Im5hbWUiOiJjb3NpbmUiLCJ0b29sVXNlSWQiOiJ0b29sdXNlX1FrbHJFSEtqUnU2T2M0QlFVZnk3WlEifX19CnsiZGVsdGEiOnsidGV4dCI6IkRvbiJ9fQp7ImRlbHRhIjp7InRleHQiOiIndCB3b3JyeSwgIEknbSBoZXJlIHRvIGhlbHAuIEl0In19CnsiZGVsdGEiOnsidGV4dCI6IiBzZWVtcyBsaWtlIHlvdSdyZSB0ZXN0aW5nIG15IGFiaWxpdHkgdG8gcmVzcG9uZCBhcHByb3ByaWF0ZWx5In19Cnsic3RvcFJlYXNvbiI6InRvb2xfdXNlIn0KeyJ1c2FnZSI6eyJpbnB1dFRva2VucyI6NDEsICJvdXRwdXRUb2tlbnMiOjM2LCAidG90YWxUb2tlbnMiOjc3fX0K]
[testupstream] header "X-Response-Type": [aws-event-stream]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Request-Id": [ab60f4bf-5e0b-44ff-bd2e-a538a1fedfdc]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Test-Backend": [aws-bedrock]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "Content-Length": [77]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no response headers
[testupstream] response line sent: {"role":"assistant"}
[testupstream] response line sent: {"start":{"toolUse":{"name":"cosine","toolUseId":"tooluse_QklrEHKjRu6Oc4BQUfy7ZQ"}}}
[testupstream] response line sent: {"delta":{"text":"Don"}}
[testupstream] response line sent: {"delta":{"text":"'t worry,  I'm here to help. It"}}
[testupstream] response line sent: {"delta":{"text":" seems like you're testing my ability to respond appropriately"}}
[testupstream] response line sent: {"stopReason":"tool_use"}
[testupstream] response line sent: {"usage":{"inputTokens":41, "outputTokens":36, "totalTokens":77}}
[testupstream] response sent
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Path": [L3YxL2NoYXQvY29tcGxldGlvbnM=]
[testupstream] header "X-Response-Body": [CnsiaWQiOiJjaGF0Y21wbC1mb28iLCJvYmplY3QiOiJjaGF0LmNvbXBsZXRpb24uY2h1bmsiLCJjcmVhdGVkIjoxNzMxNjE4MjIyLCJtb2RlbCI6ImdwdC00by1taW5pLTIwMjQtMDctMTgiLCJzeXN0ZW1fZmluZ2VycHJpbnQiOiJmcF8wYmEwZDEyNGYxIiwiY2hvaWNlcyI6W3siaW5kZXgiOjAsImRlbHRhIjp7InJvbGUiOiJhc3Npc3RhbnQiLCJjb250ZW50IjoiIiwicmVmdXNhbCI6bnVsbH0sImxvZ3Byb2JzIjpudWxsLCJmaW5pc2hfcmVhc29uIjpudWxsfV0sInVzYWdlIjpudWxsfQp7ImlkIjoiY2hhdGNtcGwtZm9vIiwib2JqZWN0IjoiY2hhdC5jb21wbGV0aW9uLmNodW5rIiwiY3JlYXRlZCI6MTczMTYxODIyMiwibW9kZWwiOiJncHQtNG8tbWluaS0yMDI0LTA3LTE4Iiwic3lzdGVtX2ZpbmdlcnByaW50IjoiZnBfMGJhMGQxMjRmMSIsImNob2ljZXMiOltdLCJ1c2FnZSI6eyJwcm9tcHRfdG9rZW5zIjoxMywiY29tcGxldGlvbl90b2tlbnMiOjEyLCJ0b3RhbF90b2tlbnMiOjI1LCJwcm9tcHRfdG9rZW5zX2RldGFpbHMiOnsiY2FjaGVkX3Rva2VucyI6MCwiYXVkaW9fdG9rZW5zIjowfSwiY29tcGxldGlvbl90b2tlbnNfZGV0YWlscyI6eyJyZWFzb25pbmdfdG9rZW5zIjowLCJhdWRpb190b2tlbnMiOjAsImFjY2VwdGVkX3ByZWRpY3Rpb25fdG9rZW5zIjowLCJyZWplY3RlZF9wcmVkaWN0aW9uX3Rva2VucyI6MH19fQpbRE9ORV0K]
[testupstream] header "X-Response-Type": [sse]
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Request-Id": [d9c7afe1-ca41-4760-b36f-99ec791f5d27]
[testupstream] header "Content-Length": [99]
[testupstream] header "X-Response-Status": []
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response line sent: {"id":"chatcmpl-foo","object":"chat.completion.chunk","created":1731618222,"model":"gpt-4o-mini-2024-07-18","system_fingerprint":"fp_0ba0d124f1","choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-foo","object":"chat.completion.chunk","created":1731618222,"model":"gpt-4o-mini-2024-07-18","system_fingerprint":"fp_0ba0d124f1","choices":[],"usage":{"prompt_tokens":13,"completion_tokens":12,"total_tokens":25,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}
[testupstream] response line sent: [DONE]
[testupstream] response sent
[testupstream] header "X-Request-Id": [0c4be5dd-26da-4081-955f-f0ea93227524]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "Content-Length": [99]
[testupstream] header "X-Response-Body": [eyJlcnJvciI6IHsibWVzc2FnZSI6ICJtaXNzaW5nIHJlcXVpcmVkIGZpZWxkIiwgInR5cGUiOiAiQmFkUmVxdWVzdEVycm9yIiwgImNvZGUiOiAiNDAwIn19]
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Path": [L3YxL2NoYXQvY29tcGxldGlvbnM=]
[testupstream] header "X-Response-Status": [400]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"error": {"message": "missing required field", "type": "BadRequestError", "code": "400"}}
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "Content-Length": [77]
[testupstream] header "X-Expected-Path": [L21vZGVsL3NvbWV0aGluZy9jb252ZXJzZS1zdHJlYW0=]
[testupstream] header "X-Response-Body": [eyJtZXNzYWdlIjogImF3cyBiZWRyb2NrIHJhdGUgbGltaXQgZXhjZWVkZWQifQ==]
[testupstream] header "X-Response-Headers": [eC1hbXpuLWVycm9ydHlwZTpUaHJvdHRsZWRFeGNlcHRpb24=]
[testupstream] header "X-Test-Backend": [aws-bedrock]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Model-Name": [something]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Response-Status": [429]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Request-Id": [33f90320-ae53-417a-84b9-76c5c41857f3]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] response headers x-amzn-errortype:ThrottledException
[testupstream] response header "x-amzn-errortype" set to ThrottledException
[testupstream] response sent: {"message": "aws bedrock rate limit exceeded"}
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Expected-Path": [L3YxL3Byb2plY3RzL2djcC1wcm9qZWN0LW5hbWUvbG9jYXRpb25zL2djcC1yZWdpb24vcHVibGlzaGVycy9nb29nbGUvbW9kZWxzL2dlbWluaS0xLjUtcHJvOmdlbmVyYXRlQ29udGVudA==]
[testupstream] header "X-Response-Body": [eyJlcnJvciI6eyJjb2RlIjo0MDAsIm1lc3NhZ2UiOiJJbnZhbGlkIHJlcXVlc3Q6IG1pc3NpbmcgcmVxdWlyZWQgZmllbGQiLCJzdGF0dXMiOiJJTlZBTElEX0FSR1VNRU5UIn19]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "Content-Length": [126]
[testupstream] header "X-Response-Status": [400]
[testupstream] header "X-Test-Backend": [gcp-vertexai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Request-Id": [f36430d2-27e3-4621-b5ff-0b3d5641ff3a]
[testupstream] header "X-Model-Name": [gemini-1.5-pro]
[testupstream] header "Authorization": [***
[testupstream] no expected host: got gcp-region-aiplatform.googleapis.com
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"error":{"code":400,"message":"Invalid request: missing required field","status":"INVALID_ARGUMENT"}}
[testupstream] header "X-Request-Id": [6f6521f0-10d8-467a-b53c-1e8fbc1584ec]
[testupstream] header "X-Model-Name": [text-embedding-ada-002]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/embeddings]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "Content-Length": [85]
[testupstream] header "X-Expected-Path": [L3YxL2VtYmVkZGluZ3M=]
[testupstream] header "X-Response-Body": [eyJvYmplY3QiOiJsaXN0IiwiZGF0YSI6W3sib2JqZWN0IjoiZW1iZWRkaW5nIiwiZW1iZWRkaW5nIjpbMC4wMDIzMDY0MjU1LC0wLjAwOTMyNzI5MiwtMC4wMDI4ODQyMjIyXSwiaW5kZXgiOjB9XSwibW9kZWwiOiJ0ZXh0LWVtYmVkZGluZy1hZGEtMDAyIiwidXNhZ2UiOnsicHJvbXB0X3Rva2VucyI6OCwidG90YWxfdG9rZW5zIjo4fX0=]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"object":"list","data":[{"object":"embedding","embedding":[0.0023064255,-0.009327292,-0.0028842222],"index":0}],"model":"text-embedding-ada-002","usage":{"prompt_tokens":8,"total_tokens":8}}
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Request-Id": [0a568494-999e-4426-a380-72d010eb7b16]
[testupstream] header "Content-Length": [85]
[testupstream] header "X-Expected-Path": [L3YxL2VtYmVkZGluZ3M=]
[testupstream] header "X-Response-Body": [eyJvYmplY3QiOiJsaXN0IiwiZGF0YSI6W3sib2JqZWN0IjoiZW1iZWRkaW5nIiwiZW1iZWRkaW5nIjpbMC4wMDIzMDY0MjU1LC0wLjAwOTMyNzI5MiwtMC4wMDI4ODQyMjIyXSwiaW5kZXgiOjB9XSwibW9kZWwiOiJ0ZXh0LWVtYmVkZGluZy1hZGEtMDAyIiwidXNhZ2UiOnsicHJvbXB0X3Rva2VucyI6OCwidG90YWxfdG9rZW5zIjo4fX0=]
[testupstream] header "X-Response-Status": []
[testupstream] header "X-Response-Type": [gzip]
[testupstream] header "X-Model-Name": [text-embedding-ada-002]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/embeddings]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: ��L��
�@�w��w��Z��"g��������A�o��
���� ����H}tl5�~��Yj:qF看�����/\�\�93s��OQW�Ø����w>����%t
�0?�8���C�R:X�0��}��	R�
[testupstream] header "Content-Length": [45]
[testupstream] header "X-Expected-Path": [L3YxL2VtYmVkZGluZ3M=]
[testupstream] header "X-Response-Body": [eyJlcnJvciI6IHsibWVzc2FnZSI6ICJpbnB1dCBjYW5ub3QgYmUgZW1wdHkiLCAidHlwZSI6ICJCYWRSZXF1ZXN0RXJyb3IiLCAiY29kZSI6ICI0MDAifX0=]
[testupstream] header "X-Response-Status": [400]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/embeddings]
[testupstream] header "User-Agent": [Go-http-client/1.1]
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Request-Id": [f26b29e5-9748-4c90-94f6-23b7bde45287]
[testupstream] header "X-Model-Name": [text-embedding-ada-002]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response sent: {"error": {"message": "input cannot be empty", "type": "BadRequestError", "code": "400"}}
[testupstream] header "X-Stainless-Runtime-Version": [go1.24.4]
[testupstream] header "Content-Type": [application/json]
[testupstream] header "X-Stainless-Retry-Count": [0]
[testupstream] header "Accept-Encoding": [gzip]
[testupstream] header "X-Forwarded-Proto": [http]
[testupstream] header "X-Request-Id": [927cd75d-ad95-4fac-8517-406ce18e156d]
[testupstream] header "X-Ai-Eg-Original-Path": [/v1/chat/completions]
[testupstream] header "X-Response-Type": [sse]
[testupstream] header "X-Stainless-Os": [Linux]
[testupstream] header "X-Stainless-Package-Version": [1.8.2]
[testupstream] header "X-Stainless-Runtime": [go]
[testupstream] header "User-Agent": [OpenAI/Go 1.8.2]
[testupstream] header "Content-Length": [95]
[testupstream] header "Accept": [application/json]
[testupstream] header "X-Response-Body": [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]
[testupstream] header "X-Stainless-Arch": [x64]
[testupstream] header "X-Stainless-Lang": [go]
[testupstream] header "X-Test-Backend": [openai]
[testupstream] header "X-Model-Name": [something]
[testupstream] no expected host: got localhost:1062
[testupstream] no expected headers
[testupstream] no non-expected headers in the request
[testupstream] no expected testupstream-id
[testupstream] no expected request body
[testupstream] no response headers
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" This"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" is"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" a"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" test"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" This"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" is"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" a"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" test"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" This"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" is"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" a"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" test"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" This"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" is"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" a"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":" test"},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}
[testupstream] response line sent: {"id":"chatcmpl-B8ZKlXBoEXZVTtv3YBmewxuCpNW7b","object":"chat.completion.chunk","created":1741382147,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_06737a9306","choices":[],"usage":{"prompt_tokens":25,"completion_tokens":61,"total_tokens":86,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}
[testupstream] response line sent: [DONE]
[testupstream] response sent
--- FAIL: TestWithTestUpstream (70.52s)
    --- FAIL: TestWithTestUpstream/metrics (0.01s)
        testupstream_test.go:471: 
            	Error Trace:	/home/<USER>/work/ai-gateway/ai-gateway/tests/extproc/testupstream_test.go:471
            	Error:      	
            	Test:       	TestWithTestUpstream/metrics
FAIL
FAIL	github.com/envoyproxy/ai-gateway/tests/extproc	78.596s
FAIL
make: *** [Makefile:158: test-extproc] Error 1
