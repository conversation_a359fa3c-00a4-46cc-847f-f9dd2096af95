make[1]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[2]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[3]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
-> Building controller for linux/amd64
go: downloading go.uber.org/zap v1.27.0
go: downloading github.com/envoyproxy/gateway v0.5.0-rc.1.0.20250711030044-64f3576cffa8
go: downloading google.golang.org/grpc v1.73.0
go: downloading k8s.io/api v0.33.2
go: downloading sigs.k8s.io/controller-runtime v0.21.0
go: downloading github.com/envoyproxy/go-control-plane/envoy v1.32.5-0.20250622153809-434b6986176d
go: downloading github.com/envoyproxy/go-control-plane v0.13.5-0.20250622153809-434b6986176d
go: downloading github.com/go-logr/logr v1.4.3
go: downloading google.golang.org/protobuf v1.36.6
go: downloading github.com/Azure/azure-sdk-for-go/sdk/azcore v1.18.0
go: downloading github.com/google/uuid v1.6.0
go: downloading k8s.io/apiextensions-apiserver v0.33.2
go: downloading k8s.io/apimachinery v0.34.0-alpha.0
go: downloading k8s.io/client-go v0.33.2
go: downloading k8s.io/utils v0.0.0-20250502105355-0f33e8f1c979
go: downloading sigs.k8s.io/gateway-api-inference-extension v0.4.0
go: downloading sigs.k8s.io/gateway-api v1.3.1-0.20250527223622-54df0a899c1c
go: downloading sigs.k8s.io/yaml v1.5.0
go: downloading go.uber.org/multierr v1.11.0
go: downloading golang.org/x/net v0.41.0
go: downloading github.com/gogo/protobuf v1.3.2
go: downloading gomodules.xyz/jsonpatch/v2 v2.5.0
go: downloading github.com/evanphx/json-patch/v5 v5.9.11
go: downloading github.com/go-logr/zapr v1.3.0
go: downloading github.com/evanphx/json-patch v5.9.11+incompatible
go: downloading github.com/cncf/xds/go v0.0.0-20250326154945-ae57f3c0d45f
go: downloading github.com/envoyproxy/protoc-gen-validate v1.2.1
go: downloading google.golang.org/genproto/googleapis/rpc v0.0.0-20250603155806-513f23925822
go: downloading github.com/Azure/azure-sdk-for-go/sdk/azidentity v1.10.1
go: downloading github.com/coreos/go-oidc/v3 v3.14.1
go: downloading golang.org/x/oauth2 v0.30.0
go: downloading github.com/aws/aws-sdk-go-v2 v1.36.5
go: downloading github.com/aws/aws-sdk-go-v2/config v1.29.17
go: downloading github.com/aws/aws-sdk-go-v2/service/sts v1.34.0
go: downloading google.golang.org/api v0.223.0
go: downloading github.com/google/cel-go v0.25.0
go: downloading k8s.io/klog/v2 v2.130.1
go: downloading sigs.k8s.io/randfill v1.0.0
go: downloading gopkg.in/inf.v0 v0.9.1
go: downloading sigs.k8s.io/structured-merge-diff/v4 v4.7.0
go: downloading github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822
go: downloading go.yaml.in/yaml/v2 v2.4.2
go: downloading golang.org/x/sys v0.33.0
go: downloading github.com/fsnotify/fsnotify v1.9.0
go: downloading github.com/prometheus/client_golang v1.22.0
go: downloading sigs.k8s.io/json v0.0.0-20241014173422-cfa47c3a1cc8
go: downloading k8s.io/kube-openapi v0.0.0-20250318190949-c8a335a9a2ff
go: downloading github.com/Azure/azure-sdk-for-go/sdk/internal v1.11.1
go: downloading github.com/AzureAD/microsoft-authentication-library-for-go v1.4.2
go: downloading golang.org/x/crypto v0.39.0
go: downloading github.com/go-jose/go-jose/v4 v4.1.0
go: downloading github.com/aws/smithy-go v1.22.4
go: downloading github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.36
go: downloading github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.12.4
go: downloading github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.12.17
go: downloading github.com/aws/aws-sdk-go-v2/credentials v1.17.70
go: downloading github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.32
go: downloading github.com/aws/aws-sdk-go-v2/internal/ini v1.8.3
go: downloading github.com/aws/aws-sdk-go-v2/service/sso v1.25.5
go: downloading github.com/aws/aws-sdk-go-v2/service/ssooidc v1.30.3
go: downloading cel.dev/expr v0.24.0
go: downloading google.golang.org/genproto/googleapis/api v0.0.0-20250603155806-513f23925822
go: downloading github.com/json-iterator/go v1.1.12
go: downloading golang.org/x/time v0.11.0
go: downloading github.com/google/btree v1.1.3
go: downloading github.com/google/gnostic-models v0.6.9
go: downloading github.com/fxamacker/cbor/v2 v2.7.0
go: downloading golang.org/x/term v0.32.0
go: downloading github.com/spf13/pflag v1.0.6
go: downloading github.com/beorn7/perks v1.0.1
go: downloading github.com/cespare/xxhash/v2 v2.3.0
go: downloading github.com/prometheus/client_model v0.6.2
go: downloading github.com/prometheus/common v0.65.0
go: downloading github.com/prometheus/procfs v0.16.1
go: downloading gopkg.in/yaml.v3 v3.0.1
go: downloading github.com/go-openapi/jsonreference v0.21.0
go: downloading github.com/go-openapi/swag v0.23.1
go: downloading github.com/google/go-cmp v0.7.0
go: downloading github.com/kylelemons/godebug v1.1.0
go: downloading github.com/pkg/browser v0.0.0-20240102092130-5ac0b6a4141c
go: downloading github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.36
go: downloading github.com/stoewer/go-strcase v1.3.0
go: downloading github.com/antlr4-go/antlr/v4 v4.13.1
go: downloading github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
go: downloading github.com/modern-go/reflect2 v1.0.2
go: downloading golang.org/x/text v0.26.0
go: downloading github.com/x448/float16 v0.8.4
go: downloading github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
go: downloading golang.org/x/sync v0.15.0
go: downloading github.com/go-openapi/jsonpointer v0.21.1
go: downloading github.com/mailru/easyjson v0.9.0
go: downloading github.com/golang-jwt/jwt/v5 v5.2.2
go: downloading golang.org/x/exp v0.0.0-20250606033433-dcc06ee1d476
go: downloading gopkg.in/evanphx/json-patch.v4 v4.12.0
go: downloading github.com/josharian/intern v1.0.0
go: downloading github.com/emicklei/go-restful/v3 v3.12.1
go: downloading github.com/pkg/errors v0.9.1
go: downloading cloud.google.com/go/auth v0.15.0
go: downloading github.com/googleapis/gax-go/v2 v2.14.1
go: downloading cloud.google.com/go/auth/oauth2adapt v0.2.7
go: downloading cloud.google.com/go v0.116.0
go: downloading go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.61.0
go: downloading cloud.google.com/go/compute/metadata v0.7.0
go: downloading github.com/google/s2a-go v0.1.9
go: downloading github.com/googleapis/enterprise-certificate-proxy v0.3.4
go: downloading github.com/felixge/httpsnoop v1.0.4
go: downloading go.opentelemetry.io/otel v1.37.0
go: downloading go.opentelemetry.io/otel/metric v1.37.0
go: downloading go.opentelemetry.io/otel/trace v1.37.0
go: downloading github.com/go-logr/stdr v1.2.2
go: downloading go.opentelemetry.io/auto/sdk v1.1.0
<- Built out/controller-linux-amd64
make[3]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
docker buildx build . -t docker.io/envoyproxy/ai-gateway-controller:latest --build-arg COMMAND_NAME=controller  --load
#0 building with "builder-355d4d00-b883-4221-ab21-a6ff58cdbdef" instance using docker-container driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 406B done
#1 DONE 0.0s

#2 [internal] load metadata for gcr.io/distroless/static-debian11:nonroot
#2 DONE 0.9s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [1/2] FROM gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed
#4 resolve gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed done
#4 sha256:9112d77ee5b16873acaa186b816c3c61f5f8eba40730e729e9614a27f40211e0 0B / 122.56kB 0.2s
#4 sha256:1c56d6035a42c0a75d79cc88acf6c9d4104343639f19b8262b520c449731445d 0B / 104.12kB 0.2s
#4 sha256:9ef7d74bdfdf3c517b28bd694a9159e94e5f53ff1ca87b39f8ca1ac0be2ed317 320B / 320B 0.2s done
#4 sha256:33e068de264953dfdc9f9ada207e76b61159721fd64a4820b320d05133a55fb8 0B / 122B 0.2s
#4 sha256:9112d77ee5b16873acaa186b816c3c61f5f8eba40730e729e9614a27f40211e0 122.56kB / 122.56kB 0.3s done
#4 sha256:1c56d6035a42c0a75d79cc88acf6c9d4104343639f19b8262b520c449731445d 104.12kB / 104.12kB 0.3s done
#4 sha256:33e068de264953dfdc9f9ada207e76b61159721fd64a4820b320d05133a55fb8 122B / 122B 0.3s done
#4 extracting sha256:1c56d6035a42c0a75d79cc88acf6c9d4104343639f19b8262b520c449731445d 0.0s done
#4 sha256:4aa0ea1413d37a58615488592a0b827ea4b2e48fa5a77cf707d0e35f025e613f 385B / 385B 0.2s done
#4 sha256:b6824ed73363f94b3b2b44084c51c31bc32af77a96861d49e16f91e3ab6bed71 67B / 67B 0.2s done
#4 sha256:5664b15f108bf9436ce3312090a767300800edbbfd4511aa1a6d64357024d5dd 0B / 168B 0.2s
#4 ...

#5 [internal] load build context
#5 transferring context: 104.56MB 0.6s done
#5 DONE 0.6s

#4 [1/2] FROM gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed
#4 sha256:5664b15f108bf9436ce3312090a767300800edbbfd4511aa1a6d64357024d5dd 168B / 168B 0.2s done
#4 sha256:27be814a09ebd97fac6fb7b82d19f117185e90601009df3fbab6f442f85cd6b3 93B / 93B 0.2s done
#4 sha256:7c12895b777bcaa8ccae0605b4de635b68fc32d60fa08f421dc3818bf55ee212 0B / 188B 0.2s
#4 sha256:7c12895b777bcaa8ccae0605b4de635b68fc32d60fa08f421dc3818bf55ee212 188B / 188B 0.2s done
#4 sha256:473d8557b1b27974f7dc7c4b4e1a209df0e27e8cae1e3e33b7bb45c969b6fc7e 0B / 755.28kB 0.2s
#4 sha256:e33bce57de289fffd2380f73997dfb7e1ec193877904bed99f28c715d071fdc4 21.19kB / 21.19kB 0.2s done
#4 sha256:473d8557b1b27974f7dc7c4b4e1a209df0e27e8cae1e3e33b7bb45c969b6fc7e 755.28kB / 755.28kB 0.3s done
#4 extracting sha256:e33bce57de289fffd2380f73997dfb7e1ec193877904bed99f28c715d071fdc4 done
#4 extracting sha256:473d8557b1b27974f7dc7c4b4e1a209df0e27e8cae1e3e33b7bb45c969b6fc7e
#4 extracting sha256:473d8557b1b27974f7dc7c4b4e1a209df0e27e8cae1e3e33b7bb45c969b6fc7e 0.2s done
#4 DONE 1.0s

#4 [1/2] FROM gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed
#4 extracting sha256:b6824ed73363f94b3b2b44084c51c31bc32af77a96861d49e16f91e3ab6bed71 done
#4 extracting sha256:7c12895b777bcaa8ccae0605b4de635b68fc32d60fa08f421dc3818bf55ee212 done
#4 extracting sha256:33e068de264953dfdc9f9ada207e76b61159721fd64a4820b320d05133a55fb8 done
#4 extracting sha256:5664b15f108bf9436ce3312090a767300800edbbfd4511aa1a6d64357024d5dd done
#4 extracting sha256:27be814a09ebd97fac6fb7b82d19f117185e90601009df3fbab6f442f85cd6b3 done
#4 extracting sha256:4aa0ea1413d37a58615488592a0b827ea4b2e48fa5a77cf707d0e35f025e613f done
#4 extracting sha256:9ef7d74bdfdf3c517b28bd694a9159e94e5f53ff1ca87b39f8ca1ac0be2ed317 done
#4 extracting sha256:9112d77ee5b16873acaa186b816c3c61f5f8eba40730e729e9614a27f40211e0 0.0s done
#4 DONE 1.0s

#6 [2/2] COPY ./out/controller-linux-amd64 /app
#6 DONE 1.9s

#7 exporting to docker image format
#7 exporting layers
#7 exporting layers 2.8s done
#7 exporting manifest sha256:62d0357cf66dd812366358c4f430e2e52f78ffec21bff20110ca2cc35deb66ae done
#7 exporting config sha256:9b42eca18e9b064152eccfe67aeeceda844b62d675bc3172e3c510b6afa7707b 0.0s done
#7 sending tarball
#7 sending tarball 1.1s done
#7 DONE 4.0s

#8 importing to docker
#8 loading layer 5342a2647e87 104.12kB / 104.12kB 0.9s done
#8 loading layer 577c8ee06f39 21.19kB / 21.19kB 0.9s done
#8 loading layer 9ed498e122b2 755.28kB / 755.28kB 0.9s done
#8 loading layer 4d049f83d9cf 67B / 67B 0.5s done
#8 loading layer af5aa97ebe6c 188B / 188B 0.5s done
#8 loading layer ac805962e479 122B / 122B 0.5s done
#8 loading layer bbb6cacb8c82 168B / 168B 0.5s done
#8 loading layer 2a92d6ac9e4f 93B / 93B 0.5s done
#8 loading layer 1a73b54f556b 385B / 385B 0.5s done
#8 loading layer c048279a7d9f 320B / 320B 0.5s done
#8 loading layer 2388d21e8e2b 122.56kB / 122.56kB 0.4s done
#8 loading layer 4e21ca9a7f22 42.01MB / 42.01MB 0.4s done
#8 DONE 0.9s
make[2]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[2]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[3]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
-> Building extproc for linux/amd64
go: downloading go.opentelemetry.io/otel/exporters/prometheus v0.59.0
go: downloading go.opentelemetry.io/otel/sdk/metric v1.37.0
go: downloading github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.6.11
go: downloading github.com/tidwall/sjson v1.2.5
go: downloading google.golang.org/genai v1.14.0
go: downloading github.com/tidwall/gjson v1.18.0
go: downloading go.opentelemetry.io/otel/sdk v1.37.0
go: downloading github.com/tidwall/match v1.1.1
go: downloading github.com/tidwall/pretty v1.2.1
go: downloading github.com/gorilla/websocket v1.5.4-0.20250319132907-e064f32e3674
<- Built out/extproc-linux-amd64
make[3]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
docker buildx build . -t docker.io/envoyproxy/ai-gateway-extproc:latest --build-arg COMMAND_NAME=extproc  --load
#0 building with "builder-355d4d00-b883-4221-ab21-a6ff58cdbdef" instance using docker-container driver

#1 [internal] load build definition from Dockerfile
#1 DONE 0.0s

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 406B done
#1 DONE 0.0s

#2 [internal] load metadata for gcr.io/distroless/static-debian11:nonroot
#2 DONE 0.2s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [1/2] FROM gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed
#4 resolve gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed done
#4 CACHED

#5 [internal] load build context
#5 transferring context: 38.58MB 0.3s done
#5 DONE 0.3s

#6 [2/2] COPY ./out/extproc-linux-amd64 /app
#6 DONE 0.1s

#7 exporting to docker image format
#7 exporting layers
#7 exporting layers 1.2s done
#7 exporting manifest sha256:784cf11fdaea46b3bc4c1907add456484fa7379ffbb24b336eb89f50e7ad2c5e done
#7 exporting config sha256:04497802629c2bb0a42677f71fe17d22a55708a95e610018eb196b4f4ebd4208 done
#7 sending tarball
#7 sending tarball 0.3s done
#7 DONE 1.5s

#8 importing to docker
#8 loading layer dd3bcc75d01a 17.65MB / 17.65MB 0.2s done
#8 DONE 0.2s
make[2]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[1]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[1]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
make[2]: Entering directory '/home/<USER>/work/ai-gateway/ai-gateway'
-> Building testupstream for linux/amd64
<- Built out/testupstream-linux-amd64
make[2]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
docker buildx build . -t docker.io/envoyproxy/ai-gateway-testupstream:latest --build-arg COMMAND_NAME=testupstream  --load
#0 building with "builder-355d4d00-b883-4221-ab21-a6ff58cdbdef" instance using docker-container driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 406B done
#1 DONE 0.0s

#2 [internal] load metadata for gcr.io/distroless/static-debian11:nonroot
#2 DONE 0.2s

#3 [internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [1/2] FROM gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed
#4 resolve gcr.io/distroless/static-debian11:nonroot@sha256:63ebe035fbdd056ed682e6a87b286d07d3f05f12cb46f26b2b44fc10fc4a59ed done
#4 CACHED

#5 [internal] load build context
#5 transferring context: 8.66MB 0.1s done
#5 DONE 0.1s

#6 [2/2] COPY ./out/testupstream-linux-amd64 /app
#6 DONE 0.0s

#7 exporting to docker image format
#7 exporting layers
#7 exporting layers 0.3s done
#7 exporting manifest sha256:c0fae759e1d0a54e3b928021f4d817ed160fdba436bb64bfc2151cd25e7c51f9 done
#7 exporting config sha256:0761d47273ddbc21bc49b9bd9b13731bdd7872491d744d86cc3861407fa06c5a done
#7 sending tarball 0.1s done
#7 DONE 0.4s

#8 importing to docker
#8 loading layer 721f9b56c5db 4.88MB / 4.88MB 0.1s done
#8 DONE 0.1s
make[1]: Leaving directory '/home/<USER>/work/ai-gateway/ai-gateway'
Run E2E tests
go: downloading github.com/openai/openai-go v1.8.2
go: downloading github.com/stretchr/testify v1.10.0
go: downloading github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2
=== INIT LOG: Setting up the kind cluster
=== INIT LOG: 	Creating kind cluster named envoy-ai-gateway
=== INIT LOG: 	Switching kubectl context to envoy-ai-gateway
Set kubectl context to "kind-envoy-ai-gateway"
=== INIT LOG: 	Loading Docker images into kind cluster
Image: "docker.io/envoyproxy/ai-gateway-controller:latest" with ID "sha256:9b42eca18e9b064152eccfe67aeeceda844b62d675bc3172e3c510b6afa7707b" not yet present on node "envoy-ai-gateway-control-plane", loading...
Image: "docker.io/envoyproxy/ai-gateway-extproc:latest" with ID "sha256:04497802629c2bb0a42677f71fe17d22a55708a95e610018eb196b4f4ebd4208" not yet present on node "envoy-ai-gateway-control-plane", loading...
Image: "docker.io/envoyproxy/ai-gateway-testupstream:latest" with ID "sha256:0761d47273ddbc21bc49b9bd9b13731bdd7872491d744d86cc3861407fa06c5a" not yet present on node "envoy-ai-gateway-control-plane", loading...
=== INIT LOG: 	done (took 35.74s in total)
=== INIT LOG: Installing Envoy Gateway
=== INIT LOG: 	Helm Install
go: downloading helm.sh/helm/v3 v3.18.4
go: downloading github.com/gofrs/flock v0.12.1
go: downloading github.com/Masterminds/semver/v3 v3.4.0
go: downloading github.com/moby/term v0.5.2
go: downloading github.com/gosuri/uitable v0.0.4
go: downloading github.com/Masterminds/semver v1.5.0
go: downloading k8s.io/kubectl v0.33.2
go: downloading github.com/fatih/color v1.18.0
go: downloading github.com/cpuguy83/go-md2man/v2 v2.0.7
go: downloading github.com/Masterminds/sprig/v3 v3.3.0
go: downloading k8s.io/cli-runtime v0.33.2
go: downloading github.com/Masterminds/sprig v2.22.0+incompatible
go: downloading github.com/cyphar/filepath-securejoin v0.4.1
go: downloading github.com/mitchellh/copystructure v1.2.0
go: downloading github.com/xeipuuv/gojsonschema v1.2.0
go: downloading github.com/Masterminds/vcs v1.13.3
go: downloading github.com/hashicorp/go-multierror v1.1.1
go: downloading github.com/containerd/containerd v1.7.27
go: downloading github.com/opencontainers/image-spec v1.1.1
go: downloading oras.land/oras-go/v2 v2.6.0
go: downloading github.com/Masterminds/squirrel v1.5.4
go: downloading github.com/jmoiron/sqlx v1.4.0
go: downloading github.com/lib/pq v1.10.9
go: downloading github.com/rubenv/sql-migrate v1.8.0
go: downloading github.com/mattn/go-runewidth v0.0.16
go: downloading github.com/mattn/go-colorable v0.1.14
go: downloading github.com/gobwas/glob v0.2.3
go: downloading github.com/russross/blackfriday/v2 v2.1.0
go: downloading dario.cat/mergo v1.0.1
go: downloading github.com/Masterminds/goutils v1.1.1
go: downloading github.com/huandu/xstrings v1.5.0
go: downloading github.com/shopspring/decimal v1.4.0
go: downloading github.com/spf13/cast v1.7.1
go: downloading github.com/liggitt/tabwriter v0.0.0-20181228230101-89fcab3d43de
go: downloading sigs.k8s.io/kustomize/api v0.19.0
go: downloading sigs.k8s.io/kustomize/kyaml v0.19.0
go: downloading github.com/mitchellh/reflectwalk v1.0.2
go: downloading github.com/hashicorp/errwrap v1.1.0
go: downloading github.com/xeipuuv/gojsonreference v0.0.0-20180127040603-bd5ef7bd5415
go: downloading github.com/opencontainers/go-digest v1.0.0
go: downloading github.com/exponent-io/jsonpath v0.0.0-20210407135951-1de76d718b3f
go: downloading k8s.io/component-base v0.33.2
go: downloading github.com/lann/builder v0.0.0-20180802200727-47ae307949d0
go: downloading github.com/go-gorp/gorp/v3 v3.1.0
go: downloading github.com/rivo/uniseg v0.4.7
go: downloading github.com/asaskevich/govalidator v0.0.0-20230301143203-a9d515a09cc2
go: downloading k8s.io/apiserver v0.33.2
go: downloading github.com/gregjones/httpcache v0.0.0-20190611155906-901d90724c79
go: downloading github.com/peterbourgon/diskv v2.0.1+incompatible
go: downloading github.com/xeipuuv/gojsonpointer v0.0.0-20190905194746-02993c407bfb
go: downloading github.com/chai2010/gettext-go v1.0.2
go: downloading github.com/MakeNowJust/heredoc v1.0.0
go: downloading github.com/mitchellh/go-wordwrap v1.0.1
go: downloading github.com/lann/ps v0.0.0-20150810152359-62de8c46ede0
go: downloading github.com/containerd/log v0.1.0
go: downloading github.com/containerd/platforms v0.2.1
go: downloading github.com/blang/semver/v4 v4.0.0
go: downloading github.com/go-errors/errors v1.4.2
go: downloading github.com/containerd/errdefs v1.0.0
go: downloading github.com/sirupsen/logrus v1.9.3
go: downloading github.com/monochromegane/go-gitignore v0.0.0-20200626010858-205db1a8cc00
go: downloading github.com/xlab/treeprint v1.2.0
go: downloading github.com/google/shlex v0.0.0-20191202100458-e7afc7fbc510
go: downloading github.com/klauspost/compress v1.18.0
go: downloading github.com/moby/spdystream v0.5.0
go: downloading github.com/mxk/go-flowrate v0.0.0-20140419014527-cca7078d478f
Release "eg" does not exist. Installing it now.
Pulled: docker.io/envoyproxy/gateway-helm:v0.0.0-latest
Digest: sha256:a744a5bb104623dc42735f0da29afe7518a529c57ea5bb2b4e68189b8db5575c
NAME: eg
LAST DEPLOYED: Fri Jul 11 13:53:16 2025
NAMESPACE: envoy-gateway-system
STATUS: deployed
REVISION: 1
TEST SUITE: None
NOTES:
**************************************************************************
*** PLEASE BE PATIENT: Envoy Gateway may take a few minutes to install ***
**************************************************************************

Envoy Gateway is an open source project for managing Envoy Proxy as a standalone or Kubernetes-based application gateway.

Thank you for installing Envoy Gateway! 🎉

Your release is named: eg. 🎉

Your release is in namespace: envoy-gateway-system. 🎉

To learn more about the release, try:

  $ helm status eg -n envoy-gateway-system
  $ helm get all eg -n envoy-gateway-system

To have a quickstart of Envoy Gateway, please refer to https://gateway.envoyproxy.io/latest/tasks/quickstart.

To get more details, please visit https://gateway.envoyproxy.io and https://github.com/envoyproxy/gateway.
=== INIT LOG: 	Applying Patch for Envoy Gateway
configmap/envoy-gateway-config serverside-applied
clusterrole.rbac.authorization.k8s.io/list-ai-gateway-controller serverside-applied
clusterrolebinding.rbac.authorization.k8s.io/list-ai-gateway-controller serverside-applied
namespace/redis-system serverside-applied
service/redis serverside-applied
deployment.apps/redis serverside-applied
=== INIT LOG: 	Restart Envoy Gateway deployment
deployment.apps/envoy-gateway restarted
=== INIT LOG: 	Waiting for Ratelimit deployment to be ready
deployment.apps/envoy-ratelimit condition met
deployment.apps/envoy-ratelimit condition met
=== INIT LOG: 	Waiting for Envoy Gateway deployment to be ready
deployment.apps/envoy-gateway condition met
deployment.apps/envoy-gateway condition met
=== INIT LOG: 	done (took 100.41s in total)
customresourcedefinition.apiextensions.k8s.io/inferencemodels.inference.networking.x-k8s.io serverside-applied
customresourcedefinition.apiextensions.k8s.io/inferencepools.inference.networking.x-k8s.io serverside-applied
=== INIT LOG: Installing AI Gateway
=== INIT LOG: 	Helm Install
Release "ai-eg-crd" does not exist. Installing it now.
NAME: ai-eg-crd
LAST DEPLOYED: Fri Jul 11 13:54:08 2025
NAMESPACE: envoy-ai-gateway-system
STATUS: deployed
REVISION: 1
TEST SUITE: None
Release "ai-eg" does not exist. Installing it now.
NAME: ai-eg
LAST DEPLOYED: Fri Jul 11 13:54:09 2025
NAMESPACE: envoy-ai-gateway-system
STATUS: deployed
REVISION: 1
TEST SUITE: None
=== INIT LOG: 	Restart AI Gateway controller
deployment.apps/ai-gateway-controller restarted
deployment.apps/ai-gateway-controller condition met
deployment.apps/ai-gateway-controller condition met
=== INIT LOG: 	done (took 7.52s in total)

=== INIT LOG: Installing Prometheus
=== INIT LOG: 	applying manifests
namespace/monitoring serverside-applied
configmap/prometheus-config serverside-applied
clusterrole.rbac.authorization.k8s.io/prometheus serverside-applied
serviceaccount/prometheus serverside-applied
clusterrolebinding.rbac.authorization.k8s.io/prometheus serverside-applied
deployment.apps/prometheus serverside-applied
service/prometheus serverside-applied
=== INIT LOG: 	waiting for deployment
deployment.apps/prometheus condition met
deployment.apps/prometheus condition met
=== INIT LOG: 	done (took 3.01s in total)

=== RUN   Test_Examples_Basic
gatewayclass.gateway.networking.k8s.io/envoy-ai-gateway-basic serverside-applied
gateway.gateway.networking.k8s.io/envoy-ai-gateway-basic serverside-applied
aigatewayroute.aigateway.envoyproxy.io/envoy-ai-gateway-basic serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-openai serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-aws serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-gcp serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-azure serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-openai-apikey serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-azure-credentials serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-aws-credentials serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-gcp-credentials serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-openai serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-aws serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-gcp serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-azure serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-openai-tls serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-aws-tls serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-gcp-tls serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-azure-tls serverside-applied
secret/envoy-ai-gateway-basic-openai-apikey serverside-applied
secret/envoy-ai-gateway-basic-azure-client-secret serverside-applied
secret/envoy-ai-gateway-basic-aws-credentials serverside-applied
secret/envoy-ai-gateway-basic-gcp-client-secret serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-testupstream serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-testupstream serverside-applied
deployment.apps/envoy-ai-gateway-basic-testupstream serverside-applied
service/envoy-ai-gateway-basic-testupstream serverside-applied
error: timed out waiting for the condition on pods/envoy-default-envoy-ai-gateway-basic-21a9f8f8-7d9f995776-tcc75
error: timed out waiting for the condition on pods/envoy-default-envoy-ai-gateway-basic-21a9f8f8-7d9f995776-tcc75
error: timed out waiting for the condition on pods/envoy-default-envoy-ai-gateway-basic-21a9f8f8-7d9f995776-tcc75
error: timed out waiting for the condition on pods/envoy-default-envoy-ai-gateway-basic-21a9f8f8-7d9f995776-tcc75
pod/envoy-default-envoy-ai-gateway-basic-21a9f8f8-7d9f995776-tcc75 condition met
=== RUN   Test_Examples_Basic/testupsream
    e2e_test.go:327: error: Get "http://127.0.0.1:45459": dial tcp 127.0.0.1:45459: connect: connection refused
Forwarding from 127.0.0.1:45459 -> 10080
Forwarding from [::1]:45459 -> 10080
Handling connection for 45459
Handling connection for 45459
    basic_test.go:95: choice: Nani?
=== RUN   Test_Examples_Basic/with_credentials
gatewayclass.gateway.networking.k8s.io/envoy-ai-gateway-basic serverside-applied
gateway.gateway.networking.k8s.io/envoy-ai-gateway-basic serverside-applied
aigatewayroute.aigateway.envoyproxy.io/envoy-ai-gateway-basic serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-openai serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-aws serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-gcp serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-azure serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-openai-apikey serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-azure-credentials serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-aws-credentials serverside-applied
backendsecuritypolicy.aigateway.envoyproxy.io/envoy-ai-gateway-basic-gcp-credentials serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-openai serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-aws serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-gcp serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-azure serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-openai-tls serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-aws-tls serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-gcp-tls serverside-applied
backendtlspolicy.gateway.networking.k8s.io/envoy-ai-gateway-basic-azure-tls serverside-applied
secret/envoy-ai-gateway-basic-openai-apikey serverside-applied
secret/envoy-ai-gateway-basic-azure-client-secret serverside-applied
secret/envoy-ai-gateway-basic-aws-credentials serverside-applied
secret/envoy-ai-gateway-basic-gcp-client-secret serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-basic-testupstream serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-basic-testupstream serverside-applied
deployment.apps/envoy-ai-gateway-basic-testupstream serverside-applied
service/envoy-ai-gateway-basic-testupstream serverside-applied
=== RUN   Test_Examples_Basic/with_credentials/openai
    basic_test.go:72: skipped due to missing credentials
=== RUN   Test_Examples_Basic/with_credentials/aws
    basic_test.go:72: skipped due to missing credentials
--- PASS: Test_Examples_Basic (36.15s)
    --- PASS: Test_Examples_Basic/testupsream (3.47s)
    --- PASS: Test_Examples_Basic/with_credentials (5.62s)
        --- SKIP: Test_Examples_Basic/with_credentials/openai (0.00s)
        --- SKIP: Test_Examples_Basic/with_credentials/aws (0.00s)
=== RUN   Test_Examples_ProviderFallback
Error from server (NotFound): error when deleting "../../examples/provider_fallback/fallback.yaml": backendtrafficpolicies.gateway.envoyproxy.io "passive-health-check" not found
    provider_fallback_test.go:43: skipped due to missing credentials
--- SKIP: Test_Examples_ProviderFallback (0.05s)
=== RUN   TestWithTestUpstream
gatewayclass.gateway.networking.k8s.io/translation-testupstream serverside-applied
gateway.gateway.networking.k8s.io/translation-testupstream serverside-applied
aigatewayroute.aigateway.envoyproxy.io/translation-testupstream serverside-applied
aiservicebackend.aigateway.envoyproxy.io/translation-testupstream-cool-model-backend serverside-applied
aiservicebackend.aigateway.envoyproxy.io/translation-testupstream-another-cool-model-backend serverside-applied
backend.gateway.envoyproxy.io/testupstream serverside-applied
backend.gateway.envoyproxy.io/testupstream-canary serverside-applied
deployment.apps/testupstream serverside-applied
service/testupstream serverside-applied
deployment.apps/testupstream-canary serverside-applied
service/testupstream-canary serverside-applied
error: timed out waiting for the condition on pods/envoy-default-translation-testupstream-2085fd18-6778dc987djlkws
pod/envoy-default-translation-testupstream-2085fd18-6778dc987djlkws condition met
=== RUN   TestWithTestUpstream//chat/completions
=== RUN   TestWithTestUpstream//chat/completions/openai
Forwarding from 127.0.0.1:33503 -> 10080
Forwarding from [::1]:33503 -> 10080
Handling connection for 33503
Handling connection for 33503
=== RUN   TestWithTestUpstream//chat/completions/aws-bedrock
Forwarding from 127.0.0.1:38025 -> 10080
Forwarding from [::1]:38025 -> 10080
Handling connection for 38025
Handling connection for 38025
=== RUN   TestWithTestUpstream//chat/completions/openai#01
Forwarding from 127.0.0.1:45167 -> 10080
Forwarding from [::1]:45167 -> 10080
Handling connection for 45167
Handling connection for 45167
--- PASS: TestWithTestUpstream (15.40s)
    --- PASS: TestWithTestUpstream//chat/completions (3.80s)
        --- PASS: TestWithTestUpstream//chat/completions/openai (1.27s)
        --- PASS: TestWithTestUpstream//chat/completions/aws-bedrock (1.26s)
        --- PASS: TestWithTestUpstream//chat/completions/openai#01 (1.26s)
=== RUN   Test_Examples_TokenRateLimit
gatewayclass.gateway.networking.k8s.io/envoy-ai-gateway-token-ratelimit serverside-applied
gateway.gateway.networking.k8s.io/envoy-ai-gateway-token-ratelimit serverside-applied
aigatewayroute.aigateway.envoyproxy.io/envoy-ai-gateway-token-ratelimit serverside-applied
aiservicebackend.aigateway.envoyproxy.io/envoy-ai-gateway-token-ratelimit-testupstream serverside-applied
backend.gateway.envoyproxy.io/envoy-ai-gateway-token-ratelimit-testupstream serverside-applied
backendtrafficpolicy.gateway.envoyproxy.io/envoy-ai-gateway-token-ratelimit-policy serverside-applied
error: timed out waiting for the condition on pods/envoy-default-envoy-ai-gateway-token-ratelimit-e3ed7007-76j89rs
pod/envoy-default-envoy-ai-gateway-token-ratelimit-e3ed7007-76j89rs condition met
Forwarding from 127.0.0.1:43701 -> 10080
Forwarding from [::1]:43701 -> 10080
Handling connection for 43701
Handling connection for 43701
Forwarding from 127.0.0.1:36177 -> 10080
Forwarding from [::1]:36177 -> 10080
Handling connection for 36177
Handling connection for 36177
Forwarding from 127.0.0.1:35737 -> 10080
Forwarding from [::1]:35737 -> 10080
Handling connection for 35737
Handling connection for 35737
Forwarding from 127.0.0.1:37307 -> 10080
Forwarding from [::1]:37307 -> 10080
Handling connection for 37307
Handling connection for 37307
Forwarding from 127.0.0.1:43627 -> 10080
Forwarding from [::1]:43627 -> 10080
Handling connection for 43627
Handling connection for 43627
Forwarding from 127.0.0.1:45387 -> 10080
Forwarding from [::1]:45387 -> 10080
Handling connection for 45387
Handling connection for 45387
Forwarding from 127.0.0.1:40061 -> 10080
Forwarding from [::1]:40061 -> 10080
Handling connection for 40061
Handling connection for 40061
Forwarding from 127.0.0.1:36097 -> 10080
Forwarding from [::1]:36097 -> 10080
Handling connection for 36097
Handling connection for 36097
Forwarding from 127.0.0.1:36821 -> 10080
Forwarding from [::1]:36821 -> 10080
Handling connection for 36821
Handling connection for 36821
Forwarding from 127.0.0.1:35495 -> 10080
Forwarding from [::1]:35495 -> 10080
Handling connection for 35495
Handling connection for 35495
Forwarding from 127.0.0.1:46833 -> 10080
Forwarding from [::1]:46833 -> 10080
Handling connection for 46833
Handling connection for 46833
Forwarding from 127.0.0.1:44313 -> 9090
Forwarding from [::1]:44313 -> 9090
Handling connection for 44313
Handling connection for 44313
    token_ratelimit_test.go:116: Response: status=200, body={"status":"success","data":{"resultType":"vector","result":[]}}
    token_ratelimit_test.go:143: 
        	Error Trace:	/home/<USER>/work/ai-gateway/ai-gateway/tests/e2e/token_ratelimit_test.go:143
        	            				/opt/hostedtoolcache/go/1.24.4/x64/src/runtime/asm_amd64.s:1700
        	Error:      	elements differ
        	            	
        	            	extra elements in list A:
        	            	([]interface {}) (len=3) {
        	            	 (string) (len=5) "input",
        	            	 (string) (len=6) "output",
        	            	 (string) (len=5) "total"
        	            	}
        	            	
        	            	
        	            	listA:
        	            	([]string) (len=3) {
        	            	 (string) (len=5) "input",
        	            	 (string) (len=6) "output",
        	            	 (string) (len=5) "total"
        	            	}
        	            	
        	            	
        	            	listB:
        	            	([]string) <nil>
        	Test:       	Test_Examples_TokenRateLimit
    token_ratelimit_test.go:103: 
        	Error Trace:	/home/<USER>/work/ai-gateway/ai-gateway/tests/e2e/token_ratelimit_test.go:103
        	Error:      	Condition never satisfied
        	Test:       	Test_Examples_TokenRateLimit
--- FAIL: Test_Examples_TokenRateLimit (134.45s)
FAIL
=== CLEANUP LOG: Collecting logs from the kind cluster
Exporting logs for cluster "envoy-ai-gateway" to:
./logs
FAIL	github.com/envoyproxy/ai-gateway/tests/e2e	333.363s
FAIL
make: *** [Makefile:172: test-e2e] Error 1
